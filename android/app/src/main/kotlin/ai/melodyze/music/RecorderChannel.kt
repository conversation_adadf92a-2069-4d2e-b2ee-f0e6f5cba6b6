package ai.melodyze.music

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.pm.PackageManager
import android.media.AudioAttributes
import android.media.AudioDeviceInfo
import android.media.AudioFormat
import android.media.AudioManager
import android.media.AudioRecord
import android.media.AudioTimestamp
import android.media.MediaPlayer
import android.media.MediaRecorder
import android.media.MediaTimestamp
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import io.flutter.plugin.common.BinaryMessenger
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import java.io.BufferedOutputStream
import java.io.File
import java.io.FileOutputStream
import java.io.OutputStream
import java.io.RandomAccessFile
import java.util.Date
import kotlin.math.log10
import kotlin.math.sqrt


class RecorderChannel(private val context: Context) : EventChannel.StreamHandler {

    companion object {
        @SuppressLint("StaticFieldLeak")
        private var shared: RecorderChannel? = null
        fun shared(context: Context): RecorderChannel {
            if (shared == null)
                shared = RecorderChannel(context)
            return shared!!
        }
    }

    private enum class RecorderState(val value: String) {
        preparing("preparing"),
        ready("ready"),
        recording("recording"),
        finishedSuccess("finishedSuccess"),
        finishedFailed("finishedFailed");

        companion object {
            fun fromValue(value: String): RecorderState? =
                entries.find { it.value == value }
        }
    }

    // Constants
    private val channelName = "recorder_channel"
    private val eventChannelStates = "recorder_channel_events"
    private val eventChannelPlayerProgress = "recorder_channel_events_player_progress"
    private val eventChannelAudioLevels = "recorder_channel_events_audio_levels"

    private var eventChannelStatesSink: EventChannel.EventSink? = null
    private var eventChannelPlayerProgressSink: EventChannel.EventSink? = null
    private var eventChannelAudioLevelsSink: EventChannel.EventSink? = null

    private var mediaPlayer: MediaPlayer? = null
    private var audioRecord: AudioRecord? = null

    private val handler: Handler = Handler(Looper.getMainLooper())

    @Volatile
    private var isRecording = false
    private var isStartedRecording = false
    private var recordingThread: Thread? = null
    private var recPath: String = ""
    private val sampleRate: Int = 48000
    private val audioSource = MediaRecorder.AudioSource.UNPROCESSED
    private val channelConfig: Int = AudioFormat.CHANNEL_IN_MONO
    private val encoding: Int = AudioFormat.ENCODING_PCM_16BIT
    private val bufferSize = AudioRecord.getMinBufferSize(sampleRate, channelConfig, encoding)
    private var currentLevelDB: Double? = null
    private var progressUpdateInterval = 0;
    private var audioLevelUpdateInterval = 0;

    private var timeStart: Long = 0
    private var timeDelay: Long = 0
    private val startCompleteSignal = CompletableDeferred<Unit>()


    fun load(messenger: BinaryMessenger) {
        MethodChannel(messenger, channelName).setMethodCallHandler { call, result ->
            CoroutineScope(Dispatchers.Main).launch {
                try {
                    val value = methodCall(call)
                    result.success(if (value is Unit) null else value)
                } catch (error: Exception) {
                    result.error(error.toString(), null, null)
                }
            }
        }

        arrayOf(
            eventChannelStates,
            eventChannelPlayerProgress,
            eventChannelAudioLevels
        ).forEach { it ->
            val eventChannel = EventChannel(messenger, it);
            eventChannel.setStreamHandler(this)
        }
    }

    private fun sendStateEvent(state: RecorderState) {
        eventChannelStatesSink?.success(state.value)
    }

    private suspend fun preparePlayer(bgmUrl: String) {

        mediaPlayer = MediaPlayer()
        mediaPlayer?.setAudioAttributes(
            AudioAttributes.Builder()
                .setContentType(AudioAttributes.CONTENT_TYPE_UNKNOWN)
                .setUsage(AudioAttributes.USAGE_MEDIA)
                .build()
        )
        mediaPlayer?.setDataSource(bgmUrl)
        mediaPlayer?.setOnCompletionListener {
            stop()
        }
        withContext(Dispatchers.IO) {
            mediaPlayer?.prepare() // this blocks, but within IO context
        }


        /*mediaPlayer = ExoPlayer.Builder(context)
            .build()
        mediaPlayer?.setMediaItem(MediaItem.fromUri(bgmUrl))
        mediaPlayer?.prepare()

        mediaPlayer?.addListener(
            object : Player.Listener {
                override fun onIsPlayingChanged(isPlaying: Boolean) {
                    if (isPlaying) {
                    }
                }
            }
        )*/
    }

    private fun startUpdatingTimeStatus() {
        if (progressUpdateInterval <= 0) {
            return
        }
        val updateTimeTask = object : Runnable {
            override fun run() {
                mediaPlayer?.let { player ->
                    if (player.isPlaying) {

                        val timestamp = player.timestamp ?: return

                        val currentPositionMs = timestamp.anchorMediaTimeUs / 1000L

                        if (timeDelay == 0L && currentPositionMs > 0) {
                            val systemNow = System.currentTimeMillis()
                            val elapsedSinceStart = systemNow - timeStart
                            val mediaToSystemDelta = elapsedSinceStart - currentPositionMs
                            val audioTimestamp = AudioTimestamp()
                            val result = audioRecord?.getTimestamp(audioTimestamp, AudioTimestamp.TIMEBASE_BOOTTIME)
                            if (result == AudioRecord.SUCCESS) {
                                val frameTimeMs = (audioTimestamp.framePosition * 1000.0 / sampleRate).toLong()
                                val audioToMediaDelta = frameTimeMs - currentPositionMs
                                timeDelay = (mediaToSystemDelta + audioToMediaDelta) / 2L
                                println("start_rec delay: $timeDelay ms")
                                startCompleteSignal.complete(Unit)
                            }
                        }


                        eventChannelPlayerProgressSink?.success(currentPositionMs);
//                        currentLevelDB?.let {
//                            eventChannelAudioLevelsSink?.success(it);
//                        }
                        handler.postDelayed(this, progressUpdateInterval.toLong())
                    }
                }
            }
        }
        handler.post(updateTimeTask)
    }

    private fun calculateRMS(buffer: ByteArray, size: Int): Double {
        var sum = 0.0
        val sampleCount = size / 2 // 2 bytes per sample (16-bit)

        for (i in 0 until size step 2) {
            if (i + 1 >= size) break

            // Little-endian: low byte + high byte
            val low = buffer[i].toInt() and 0xFF
            val high = buffer[i + 1].toInt()
            val sample = (high shl 8) or low
            val sampleSigned = sample.toShort().toInt()

            sum += sampleSigned * sampleSigned
        }

        return sqrt(sum / sampleCount)
    }

    private fun amplitudeToDb(amplitude: Double): Double {
        val maxAmplitude = Short.MAX_VALUE.toDouble() // 32767.0
        return if (amplitude <= 0.0) {
            -160.0 // iOS-style silence floor
        } else {
            (20 * log10(amplitude / maxAmplitude)).coerceAtLeast(-160.0)
        }
    }

    private fun writeWavFile() {
        val pcmData = ByteArray(bufferSize)
        val outputFile = File(recPath)
        val outputStream = BufferedOutputStream(FileOutputStream(outputFile))

        // Write a placeholder WAV header
        writeWavHeader(outputStream, sampleRate, 1, 16)

        Log.d("Recorder", "writeWavFile start")
        var totalAudioLen = 0
        while (isRecording) {
            val readBytes = audioRecord!!.read(pcmData, 0, pcmData.size)
            Log.d("Recorder", "Bytes read: $readBytes")
            if (readBytes > 0) {
                val rms = calculateRMS(pcmData, bufferSize)
                currentLevelDB = amplitudeToDb(rms)
                outputStream.write(pcmData, 0, readBytes)
                totalAudioLen += readBytes
            }
        }
        Log.d("Recorder", "writeWavFile end")

        outputStream.flush()
        outputStream.close()

        // Update WAV header now that we know final data size
        updateWavHeader(outputFile, totalAudioLen)
    }

    private fun writeWavHeader(
        out: OutputStream,
        sampleRate: Int,
        channels: Int,
        bitsPerSample: Int
    ) {
        val header = ByteArray(44)

        val byteRate = sampleRate * channels * bitsPerSample / 8

        // RIFF chunk
        header[0] = 'R'.code.toByte()
        header[1] = 'I'.code.toByte()
        header[2] = 'F'.code.toByte()
        header[3] = 'F'.code.toByte()
        // File size placeholder
        // header[4..7]
        header[8] = 'W'.code.toByte()
        header[9] = 'A'.code.toByte()
        header[10] = 'V'.code.toByte()
        header[11] = 'E'.code.toByte()

        // fmt chunk
        header[12] = 'f'.code.toByte()
        header[13] = 'm'.code.toByte()
        header[14] = 't'.code.toByte()
        header[15] = ' '.code.toByte()
        header[16] = 16
        header[17] = 0
        header[18] = 0
        header[19] = 0
        header[20] = 1
        header[21] = 0
        header[22] = channels.toByte()
        header[23] = 0
        header[24] = (sampleRate and 0xff).toByte()
        header[25] = ((sampleRate shr 8) and 0xff).toByte()
        header[26] = ((sampleRate shr 16) and 0xff).toByte()
        header[27] = ((sampleRate shr 24) and 0xff).toByte()
        header[28] = (byteRate and 0xff).toByte()
        header[29] = ((byteRate shr 8) and 0xff).toByte()
        header[30] = ((byteRate shr 16) and 0xff).toByte()
        header[31] = ((byteRate shr 24) and 0xff).toByte()
        header[32] = (channels * bitsPerSample / 8).toByte()
        header[33] = 0
        header[34] = bitsPerSample.toByte()
        header[35] = 0

        // data chunk
        header[36] = 'd'.code.toByte()
        header[37] = 'a'.code.toByte()
        header[38] = 't'.code.toByte()
        header[39] = 'a'.code.toByte()
        // data length placeholder
        // header[40..43]

        out.write(header, 0, 44)
    }

    private fun updateWavHeader(wavFile: File, totalAudioLen: Int) {
        val totalDataLen = totalAudioLen + 36
        val raf = RandomAccessFile(wavFile, "rw")

        raf.seek(4)
        raf.write(intToByteArray(totalDataLen), 0, 4)

        raf.seek(40)
        raf.write(intToByteArray(totalAudioLen), 0, 4)

        raf.close()
    }

    private fun intToByteArray(value: Int): ByteArray {
        return byteArrayOf(
            (value and 0xff).toByte(),
            ((value shr 8) and 0xff).toByte(),
            ((value shr 16) and 0xff).toByte(),
            ((value shr 24) and 0xff).toByte()
        )
    }

    private fun acceptDevices(type: Int): Boolean {
        return when (type) {
            AudioDeviceInfo.TYPE_TELEPHONY -> false
            AudioDeviceInfo.TYPE_REMOTE_SUBMIX -> false
            AudioDeviceInfo.TYPE_BLUETOOTH_SCO -> false
            /*AudioDeviceInfo.TYPE_ECHO_REFERENCE*/ 28 -> false
            else -> true
        }
    }

    private fun getAudioDeviceTypeName(type: Int): String {
        return when (type) {
            AudioDeviceInfo.TYPE_BUILTIN_EARPIECE -> "TYPE_BUILTIN_EARPIECE"
            AudioDeviceInfo.TYPE_BUILTIN_SPEAKER -> "TYPE_BUILTIN_SPEAKER"
            AudioDeviceInfo.TYPE_WIRED_HEADSET -> "TYPE_WIRED_HEADSET"
            AudioDeviceInfo.TYPE_WIRED_HEADPHONES -> "TYPE_WIRED_HEADPHONES"
            AudioDeviceInfo.TYPE_LINE_ANALOG -> "TYPE_LINE_ANALOG"
            AudioDeviceInfo.TYPE_LINE_DIGITAL -> "TYPE_LINE_DIGITAL"
            AudioDeviceInfo.TYPE_BLUETOOTH_SCO -> "TYPE_BLUETOOTH_SCO"
            AudioDeviceInfo.TYPE_BLUETOOTH_A2DP -> "TYPE_BLUETOOTH_A2DP"
            AudioDeviceInfo.TYPE_HDMI -> "TYPE_HDMI"
            AudioDeviceInfo.TYPE_HDMI_ARC -> "TYPE_HDMI_ARC"
            AudioDeviceInfo.TYPE_USB_DEVICE -> "TYPE_USB_DEVICE"
            AudioDeviceInfo.TYPE_USB_ACCESSORY -> "TYPE_USB_ACCESSORY"
            AudioDeviceInfo.TYPE_DOCK -> "TYPE_DOCK"
            AudioDeviceInfo.TYPE_FM -> "TYPE_FM"
            AudioDeviceInfo.TYPE_BUILTIN_MIC -> "TYPE_BUILTIN_MIC"
            AudioDeviceInfo.TYPE_FM_TUNER -> "TYPE_FM_TUNER"
            AudioDeviceInfo.TYPE_TV_TUNER -> "TYPE_TV_TUNER"
            AudioDeviceInfo.TYPE_TELEPHONY -> "TYPE_TELEPHONY"
            AudioDeviceInfo.TYPE_AUX_LINE -> "TYPE_AUX_LINE"
            AudioDeviceInfo.TYPE_IP -> "TYPE_IP"
            AudioDeviceInfo.TYPE_BUS -> "TYPE_BUS"
            AudioDeviceInfo.TYPE_USB_HEADSET -> "TYPE_USB_HEADSET"
            AudioDeviceInfo.TYPE_HEARING_AID -> "TYPE_HEARING_AID"
            AudioDeviceInfo.TYPE_BLE_HEADSET -> "TYPE_BLE_HEADSET"
            AudioDeviceInfo.TYPE_BLE_SPEAKER -> "TYPE_BLE_SPEAKER"
            AudioDeviceInfo.TYPE_BLE_BROADCAST -> "TYPE_BLE_BROADCAST"
            else -> "UNKNOWN_TYPE_$type"
        }
    }


    private suspend fun methodCall(call: MethodCall): Any? {
        return when (call.method) {
            "prepare" ->
                prepare(
                    call.argument<String>("recPath")!!,
                    call.argument<String>("bgmPath")!!,
                    call.argument<Int>("progressUpdateInterval")!!,
                    call.argument<Int>("audioLevelUpdateInterval")!!,
                )

            "start" ->
                start()

            "stop" ->
                stop()

            "hasPermission" ->
                hasPermission()

            "isRecording" ->
                isRecording()

            "listInputDevices" ->
                listInputDevices()

            "getSelectedDevice" ->
                getSelectedDevice()

            "selectDevice" ->
                selectDevice(call.argument<String>("uid")!!)

            "dispose" ->
                dispose()

            else -> null
        }
    }

    private suspend fun prepare(
        recPath: String,
        bgmPath: String,
        progressUpdateInterval: Int,
        audioLevelUpdateInterval: Int
    ) {
        this.recPath = recPath
        this.progressUpdateInterval = progressUpdateInterval
        this.audioLevelUpdateInterval = audioLevelUpdateInterval
        try {
            File(recPath).delete()
        } catch (_: Exception) {}

        sendStateEvent(RecorderState.preparing)
        preparePlayer(bgmPath)

        audioRecord = if (ActivityCompat.checkSelfPermission(
                context,
                Manifest.permission.RECORD_AUDIO
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            throw Exception("Record permission not granted!!")
        } else {
            AudioRecord(
                audioSource,
                sampleRate,
                channelConfig,
                encoding,
                bufferSize
            )
        }
        sendStateEvent(RecorderState.ready)
    }

    private suspend fun start(): Int {
        if (isRecording) {
            return 0
        }
        isStartedRecording = false
        isRecording = true
        recordingThread = Thread {
            writeWavFile()
        }.also { it.start() }
        sendStateEvent(RecorderState.recording)
        startUpdatingTimeStatus()
        timeStart = Date().time
        audioRecord?.startRecording()
        println("start_rec rec: ${Date().time-timeStart}")
        mediaPlayer?.start()
        timeStart = Date().time
        timeDelay = 0
        startCompleteSignal.await()
        return timeDelay.toInt()
    }

    private fun stop() {
        try {
            audioRecord?.stop()
            mediaPlayer?.stop()
            sendStateEvent(RecorderState.finishedSuccess)
        } catch (error: Exception) {
            sendStateEvent(RecorderState.finishedFailed)
        } finally {
            isRecording = false
        }
    }

    private fun hasPermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            this.context,
            android.Manifest.permission.RECORD_AUDIO
        ) == PackageManager.PERMISSION_GRANTED
    }

    private fun isRecording(): Boolean {
        return isRecording
    }

    private fun listInputDevices(): String {
        val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
        val iDevices = audioManager.getDevices(AudioManager.GET_DEVICES_INPUTS)

        val inputDevices =
            iDevices.filter { it.isSource && acceptDevices(it.type) }

        val devicesList: List<RecorderDevice> = inputDevices.map {
            println("Name: ${it.productName}, Type: ${it.type}, ID: ${it.id}")
            RecorderDevice(
                it.id.toString(),
                getAudioDeviceTypeName(it.type),
                it.productName.toString()
            )
        }
        val jsonStr = Json.encodeToString(devicesList)
        return jsonStr
    }

    private fun getSelectedDevice(): String {
        return ""
    }

    private fun selectDevice(uid: String) {

    }

    private fun dispose() {
        mediaPlayer?.release()
        audioRecord?.release()
        mediaPlayer = null
        audioRecord = null
    }

    // MARK: EventChannel.StreamHandler

    override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
        when (arguments as String) {
            eventChannelStates -> eventChannelStatesSink = events
            eventChannelPlayerProgress -> eventChannelPlayerProgressSink = events
            eventChannelAudioLevels -> eventChannelAudioLevelsSink = events
        }
    }

    override fun onCancel(arguments: Any?) {

    }
}

@Serializable
data class RecorderDevice(
    val uid: String,
    val portName: String,
    val portType: String
)