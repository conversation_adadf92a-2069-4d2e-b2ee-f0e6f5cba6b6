import 'dart:async';
import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:juce_mix_player/juce_mix_player.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/ui/atom/app_circular_progress_indicator.dart';
import 'package:melodyze/core/ui/atom/app_gradient_container.dart';
import 'package:melodyze/core/ui/molecules/butons/transparent_round_icon_button.dart';
import 'package:melodyze/core/ui/molecules/dialoges.dart';
import 'package:melodyze/core/ui/molecules/expandable_swipe_widget.dart';
import 'package:melodyze/core/ui/molecules/lyric_viewer/lyrics_viewer.dart';
import 'package:melodyze/core/ui/molecules/lyric_viewer/model/lyrics_data.dart';
import 'package:melodyze/core/ui/molecules/navigation/melo_scaffold.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/app_gradients.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';
import 'package:melodyze/modules/recording/bloc/recording_screen_bloc.dart';
import 'package:melodyze/modules/recording/bloc/recording_screen_events.dart';
import 'package:melodyze/modules/recording/bloc/recording_screen_states.dart';
import 'package:melodyze/modules/recording/cubit/download_wav_file_cubit.dart';
import 'package:melodyze/modules/recording/repo/recording_repo.dart';
import 'package:melodyze/modules/recording/service/recording_service.dart';
import 'package:melodyze/modules/recording/widget/mic_select_popup.dart';
import 'package:melodyze/modules/recording/widget/recording_button.dart';
import 'package:melodyze/modules/recording/widget/vu_meter.dart';
import 'package:melodyze/modules/share/share_data_bloc.dart';
import 'package:melodyze/modules/song_personalization/bloc/audio_player_bloc/audio_player_bloc.dart';
import 'package:melodyze/modules/song_personalization/bloc/juce_mix_player/juce_mix_bloc.dart';
import 'package:melodyze/modules/song_personalization/bloc/song_personalization_bloc.dart';

@RoutePage()
class RecordingScreen extends StatelessWidget {
  final AudioPlayerBloc audioPlayerBloc;
  final LyricsViewerBloc lyricsViewerBloc;

  const RecordingScreen({
    required this.audioPlayerBloc,
    required this.lyricsViewerBloc,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final wavFileCubit = DownloadWavFileCubit(
      recordingRepo: RecordingRepo(
        recordingService: RecordingService(),
      ),
    );

    final recordingScreenBloc = RecordingScreenBloc(
      // audioPlayerBloc: audioPlayerBloc,
      annotatedData: context.read<ShareDataBloc>().annotatedData!,
      recordingRepo: RecordingRepo(
        recordingService: RecordingService(),
      ),
      downloadWavFileCubit: wavFileCubit,
      shareDateBloc: context.read<ShareDataBloc>(),
    );
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => wavFileCubit,
        ),
        BlocProvider(
          create: (context) => recordingScreenBloc,
        ),
        BlocProvider.value(
          value: audioPlayerBloc,
        ),
        BlocProvider.value(
          value: lyricsViewerBloc,
        ),
      ],
      child: _RecordingScreen(),
    );
  }
}

class _RecordingScreen extends StatefulWidget {
  @override
  State<_RecordingScreen> createState() => _RecordingScreenState();
}

class _RecordingScreenState extends State<_RecordingScreen> {
  static const double _bottomSectionHeight = 214;
  final GlobalKey _lyricsViewerKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    final recordingScreenBloc = context.read<RecordingScreenBloc>();
    final audioPlayerBloc = context.read<AudioPlayerBloc>();
    // final annotatedData = recordingScreenBloc.annotatedData;
    final guideVocalPath = recordingScreenBloc.annotatedData.guideVocalPath;
    final metronomeVolume = audioPlayerBloc.metronomeTracks.isNotEmpty ? audioPlayerBloc.metronomeTracks.first.volume : 1.0;

    return Stack(
      children: [
        AppGradientContainer(
          borderGradient: AppGradients.gradientBlackPinkBlueBlackBorder,
          borderWidth: 8,
          borderRadius: BorderRadius.circular(36),
          child: SizedBox(
            height: MediaQuery.of(context).size.height,
            width: MediaQuery.of(context).size.width,
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(36),
            child: OverflowBox(
              maxWidth: MediaQuery.of(context).size.width,
              child: BlocBuilder<RecordingScreenBloc, BlocState>(
                builder: (context, state) {
                  return MeloScaffold(
                    popScopeDescription: 'Are you sure you want to discard the recording ?',
                    onPopScopeDiscard: () {
                      recordingScreenBloc.add(const StopRecordingEvent(isDisposed: true));
                      audioPlayerBloc.add(AudioPlayerStopEvent());
                    },
                    showBackButton: state is AudioRecorderReadyState,
                    overlayAction: Positioned(
                      top: MediaQuery.of(context).padding.top,
                      child: Center(
                        child: BlocBuilder<RecordingScreenBloc, BlocState>(
                          buildWhen: (previous, current) => current is! RecordLoadingState,
                          builder: (context, state) {
                            return BlocBuilder<AudioPlayerBloc, AudioPlayerState>(
                              buildWhen: (previous, current) => current is AudioPlayerLoaded || current is AudioPlayerError || current is AudioPlayerInitial,
                              builder: (context, audioPlayerState) {
                                if (state is! AudioRecorderReadyState) {
                                  return SizedBox();
                                }
                                return ExpandableSwipeWidget(
                                  isLoading: audioPlayerState is AudioPlayerInitial,
                                  items: [
                                    ExpandableSwipeWidgetModel(
                                      title: "Guide",
                                      label: "Guide",
                                      iconPath: AssetPaths.guide,
                                      isDisabled: audioPlayerBloc.tracks[MixerTrackType.guide] == null,
                                      initialVolume: audioPlayerBloc.tracks[MixerTrackType.guide]?.volume ?? 1.0,
                                      onVolumeChanged: guideVocalPath != null
                                          ? (value) {
                                              final guidedVocalPath = context.read<ShareDataBloc>().annotatedData?.guideVocalPath;
                                              if (guidedVocalPath == null) {
                                                return;
                                              }
                                              audioPlayerBloc.add(
                                                EnableGuideTrackEvent(
                                                  guideS3Path: context.read<ShareDataBloc>().annotatedData!.guideVocalPath!,
                                                  bgmS3Path: context.read<ShareDataBloc>().annotatedData!.songPath,
                                                  volume: value,
                                                ),
                                              );
                                            }
                                          : null,
                                      onPressed: guideVocalPath != null
                                          ? (isEnabled) {
                                              if (isEnabled) {
                                                audioPlayerBloc.add(DisableGuideTrackEvent());
                                              } else {
                                                final guidedVocalPath = context.read<ShareDataBloc>().annotatedData?.guideVocalPath;
                                                if (guidedVocalPath != null) {
                                                  audioPlayerBloc.add(EnableGuideTrackEvent(
                                                    guideS3Path: guidedVocalPath,
                                                    bgmS3Path: context.read<ShareDataBloc>().annotatedData!.songPath,
                                                  ));
                                                }
                                              }
                                            }
                                          : null,
                                    ),
                                    ExpandableSwipeWidgetModel(
                                      title: "Metronome",
                                      label: "Click",
                                      iconPath: AssetPaths.metronome,
                                      isDisabled: audioPlayerBloc.metronomeTracks.isEmpty,
                                      initialVolume: metronomeVolume ?? 0,
                                      onVolumeChanged: (value) => audioPlayerBloc.add(
                                        EnableMetronomeEvent(
                                          bgmS3Path: context.read<ShareDataBloc>().annotatedData!.songPath,
                                          timeSignature: context.read<ShareDataBloc>().song!.timeSignature,
                                          volume: value,
                                          tempo: int.tryParse(context.read<ShareDataBloc>().annotatedData!.tempo) ?? 0,
                                        ),
                                      ),
                                      onPressed: (isEnabled) {
                                        if (isEnabled) {
                                          audioPlayerBloc.add(DisableMetronomeEvent());
                                        } else {
                                          audioPlayerBloc.add(
                                            EnableMetronomeEvent(
                                              bgmS3Path: context.read<ShareDataBloc>().annotatedData!.songPath,
                                              timeSignature: context.read<ShareDataBloc>().song!.timeSignature,
                                              tempo: int.tryParse(context.read<ShareDataBloc>().annotatedData!.tempo) ?? 0,
                                            ),
                                          );
                                        }
                                      },
                                    )
                                  ],
                                );
                              },
                            );
                          },
                        ),
                      ),
                    ),
                    secondaryAction: (context) {
                      return BlocBuilder<RecordingScreenBloc, BlocState>(
                        buildWhen: (previous, current) => current is! RecordLoadingState,
                        builder: (context, state) {
                          if (state is AudioRecorderReadyState) {
                            final micList = recordingScreenBloc.micList;
                            return micList.isNotEmpty
                                ? MicSelectPopUp(
                                    micList: micList,
                                    selectedMic: recordingScreenBloc.selectedMic,
                                    onSelected: (mic) => recordingScreenBloc.add(ChangeAudioInputEvent(mic: mic)),
                                  )
                                : const SizedBox.shrink();
                          }
                          return const SizedBox.shrink();
                        },
                      );
                    },
                    showBackground: false,
                    body: SafeArea(
                      child: Stack(
                        children: [
                          BlocConsumer<RecordingScreenBloc, BlocState>(
                            listenWhen: (previous, current) => current is ShowMicPermissionDialogState,
                            listener: (context, state) async {
                              if (state is ShowMicPermissionDialogState) {
                                await showGivePermissionDialog(context: context, title: "Give Microphone Permission");
                              }
                            },
                            builder: (context, state) {
                              return Container();
                            },
                          ),
                          BlocConsumer<RecordingScreenBloc, BlocState>(
                            listenWhen: (previous, current) =>
                                current is RecordingSuccessState ||
                                current is ShowRecordingMessage ||
                                current is RecordingUpdatePlayerProgressState ||
                                current is AudioRecorderReadyState,
                            listener: (context, state) async {
                              if (state is ShowRecordingMessage) {
                                await showOkDialog(
                                  context: context,
                                  title: 'Tips:',
                                  subTitle: 'To get best recording quality\n\n- Record in noiseless environment.\n- Use headphones.\n- Maintain fair distance from mic.',
                                );
                              }
                              if (state is RecordingSuccessState && context.mounted) {
                                if (state.hasClipped) {
                                  final isConfirmed = await showYesNoDialog(
                                    context: context,
                                    title: 'Recording Alert',
                                    subTitle: 'Your recording has clipped. Do you still want to continue?',
                                  );
                                  if (isConfirmed == false) {
                                    recordingScreenBloc.add(const StopRecordingEvent(discardRecording: true));
                                    recordingScreenBloc.add(InitAudioRecorderEvent(showTips: false));
                                    return;
                                  }
                                }
                              }
                              if (state is RecordingUpdatePlayerProgressState) {
                                audioPlayerBloc.add(AudioPlayerProgressUpdateEvent(position: Duration(milliseconds: state.progressInMilliseconds)));
                              }

                              if (state is AudioRecorderReadyState) {
                                audioPlayerBloc.add(AudioPlayerProgressUpdateEvent(position: Duration(milliseconds: 0)));
                              }

                              // Reset lyrics viewer to initial position when recording stops successfully
                              if (state is RecordingSuccessState || state is RecordingErrorState) {
                                (_lyricsViewerKey.currentState as dynamic)?.resetToInitialPosition();
                              }
                            },
                            buildWhen: (previous, current) =>
                                current is! ShowRecordingMessage &&
                                (current is AudioRecorderReadyState || current is RecordingErrorState || current is AudioRecorderInitializingState),
                            builder: (context, state) {
                              // if (state is AudioRecorderInitializingState) {
                              //   return const Center(
                              //     child: AppCircularProgressIndicator(),
                              //   );
                              // }
                              if (state is RecordingErrorState) {
                                return Center(
                                  child: Text(state.errorMessage),
                                );
                              }
                              return const SizedBox.shrink();
                            },
                          ),
                          BlocBuilder<LyricsViewerBloc, BlocState>(builder: (context, state) {
                            if (state is LoadingState) {
                              return const Center(
                                child: AppCircularProgressIndicator(),
                              );
                            }
                            if (state is BlocSuccessState<LyricsData>) {
                              return Padding(
                                padding: const EdgeInsets.only(bottom: _bottomSectionHeight, left: 16.0, right: 16.0),
                                child: BlocBuilder<AudioPlayerBloc, BlocState>(
                                    buildWhen: (previous, current) => current is AudioPlayerProgressUpdated,
                                    builder: (context, playerState) {
                                      return IgnorePointer(
                                        child: RepaintBoundary(
                                          child: LyricsViewer(
                                            key: _lyricsViewerKey,
                                            lyricsData: state.data,
                                            position: playerState is AudioPlayerProgressUpdated ? playerState.position.inMilliseconds : 0,
                                          ),
                                        ),
                                      );
                                    }),
                              );
                            }
                            return const SizedBox.shrink();
                          }),
                          Positioned(
                              bottom: 0,
                              left: 0,
                              right: 0,
                              child: SizedBox(
                                height: _bottomSectionHeight,
                                child: Stack(
                                  children: [
                                    Align(
                                      alignment: Alignment.center,
                                      child: BlocBuilder<RecordingScreenBloc, BlocState>(
                                          buildWhen: (_, current) => current is! RecordingUpdatePlayerProgressState && current is! RecordingUpdateAudioLevelState,
                                          builder: (context, state) {
                                            return Padding(
                                              padding: const EdgeInsets.symmetric(horizontal: 16),
                                              child: Row(
                                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                children: [
                                                  state is RecordingSuccessState
                                                      ? GestureDetector(
                                                          onTap: () {
                                                            // if (Platform.isAndroid) {
                                                            //   audioPlayerBloc.add(AudioPlayerStopEvent());
                                                            // }
                                                            recordingScreenBloc.add(const StopRecordingEvent(discardRecording: true));
                                                            recordingScreenBloc.add(InitAudioRecorderEvent(showTips: false));

                                                            // if (Platform.isIOS) {
                                                            //   if (audioPlayerBloc.metronomeTracks.isNotEmpty) {
                                                            //     audioPlayerBloc.add(DisableMetronomeEvent());
                                                            //   }
                                                            //   if (audioPlayerBloc.tracks[MixerTrackType.guide] != null) {
                                                            //     audioPlayerBloc.add(DisableGuideTrackEvent());
                                                            //   }
                                                            // }
                                                          },
                                                          child: Padding(
                                                            padding: const EdgeInsets.only(left: 16.0),
                                                            child: _PostRecordingIcon(
                                                              label: 'Restart',
                                                              icon: AssetPaths.restartButton,
                                                              labelSize: 10,
                                                              iconSize: 28,
                                                              extraPadding: 6,
                                                            ),
                                                          ),
                                                        )
                                                      : SizedBox(
                                                          width: TransParentRoundIconButtonSize.large.size,
                                                        ),
                                                  state is RecordingSuccessState
                                                      ? GestureDetector(
                                                          onTap: () async {
                                                            // audioPlayerBloc.metronomeTracks.clear();
                                                            // audioPlayerBloc.tracks.remove(MixerTrackType.guide);
                                                            if (context.mounted) {
                                                              // audioPlayerBloc.setIfRecordingScreen(null);
                                                              // Create JuceMixBloc with minimal initial model
                                                              final juceMixPlayerBloc = JuceMixBloc(
                                                                mixComposeModel: MixerComposeModel(
                                                                  outputDuration: 0,
                                                                  tracks: [],
                                                                ),
                                                              );
                                                              unawaited(context
                                                                  .pushRoute(
                                                                VocalFiltersRoute(
                                                                  recordedVocalPath: state.outputFilePath,
                                                                  normalizedRecordingPath: state.normalizedOutputFilePath,
                                                                  downloadWavFileCubit: context.read<DownloadWavFileCubit>(),
                                                                  lyricsData: context.read<LyricsViewerBloc>().lyricsData,
                                                                  inputMic: recordingScreenBloc.selectedMic != null ? recordingScreenBloc.selectedMic!.portName : "",
                                                                  timeDiff: state.timeDiff,
                                                                  juceMixBloc: juceMixPlayerBloc,
                                                                ),
                                                              )
                                                                  .then((_) async {
                                                                // audioPlayerBloc.setIfRecordingScreen(recordingScreenBloc.record!);
                                                                logger.d("BACK TO RECORDING SCREEN ========================================================");
                                                                // if (Platform.isAndroid) {
                                                                // audioPlayerBloc.add(AudioPlayerLoadEvent(
                                                                //   path: annotatedData.songPath,
                                                                //   songId: annotatedData.masterSongId,
                                                                //   annotationId: annotatedData.id,
                                                                //   autoPlay: false,
                                                                //   type: AudioType.url,
                                                                // ));
                                                                // }
                                                                // if (Platform.isIOS) {
                                                                // await Future.delayed(const Duration(milliseconds: 500));
                                                                // }
                                                                if (context.mounted) {
                                                                  recordingScreenBloc.add(InitAudioRecorderEvent(showTips: false));
                                                                }
                                                              }));
                                                            }
                                                          },
                                                          child: Padding(
                                                            padding: const EdgeInsets.only(left: 12.0),
                                                            child: _PostRecordingIcon(
                                                              label: 'Mix & Master',
                                                              icon: AssetPaths.finalize,
                                                              labelSize: 12,
                                                              iconSize: 84,
                                                            ),
                                                          ),
                                                        )
                                                      : RecordingButton(
                                                          isLoading: state is! RecordingInProgressState &&
                                                              state is! AudioRecorderReadyState &&
                                                              state is! RecordingUpdatePlayerProgressState &&
                                                              state is! RecordingUpdateAudioLevelState,
                                                          isRecording: state is RecordingInProgressState,
                                                          onPressed: () {
                                                            if (audioPlayerBloc.state is AudioPlayerInitial) {
                                                              // show snackbar
                                                              ScaffoldMessenger.of(context).showSnackBar(
                                                                SnackBar(
                                                                  content: Text('Please wait for the audio player to initialize!'),
                                                                  duration: const Duration(seconds: 2),
                                                                ),
                                                              );
                                                              return;
                                                            }

                                                            if (state is! RecordingInProgressState) {
                                                              HapticFeedback.mediumImpact();
                                                              recordingScreenBloc.add(StartRecordingEvent());
                                                            } else {
                                                              HapticFeedback.mediumImpact();
                                                              // if (Platform.isAndroid) {
                                                              //   audioPlayerBloc.add(AudioPlayerStopEvent());
                                                              // }
                                                              recordingScreenBloc.add(const StopRecordingEvent());
                                                            }
                                                          },
                                                        ),
                                                  state is RecordingSuccessState
                                                      ? GestureDetector(
                                                          onTap: () async {
                                                            Navigator.pop(context);
                                                          },
                                                          child: Padding(
                                                            padding: const EdgeInsets.only(right: 16.0),
                                                            child: _PostRecordingIcon(
                                                              label: 'Discard',
                                                              icon: AssetPaths.discard,
                                                              labelSize: 10,
                                                            ),
                                                          ),
                                                        )
                                                      : SizedBox(
                                                          width: TransParentRoundIconButtonSize.large.size,
                                                        ),
                                                ],
                                              ),
                                            );
                                          }),
                                    )
                                  ],
                                ),
                              )),
                          if (Platform.isIOS)
                            Positioned(
                              bottom: 16,
                              width: MediaQuery.sizeOf(context).width,
                              child: VUMeter(),
                            )
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        )
      ],
    );
  }
}

class _PostRecordingIcon extends StatelessWidget {
  final String label;
  final String icon;
  final double labelSize;
  final double iconSize;
  final double extraPadding;

  const _PostRecordingIcon({
    required this.label,
    required this.icon,
    required this.labelSize,
    this.iconSize = 32,
    this.extraPadding = 0,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox.square(
          dimension: iconSize,
          child: ImageLoader.fromAsset(icon),
        ),
        SizedBox(height: extraPadding),
        Text(
          label,
          style: AppTextStyles.text10regular.copyWith(
            fontFamily: AppFonts.iceland,
            fontSize: labelSize,
          ),
        )
      ],
    );
  }
}
