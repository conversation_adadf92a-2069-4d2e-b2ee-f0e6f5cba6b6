part of 'juce_audio_bloc.dart';

abstract class JuceAudioState extends BlocState {
  const JuceAudioState();
}

class JuceAudioInit extends JuceAudioState {
  @override
  List<Object> get props => [];
}

class JuceAudioLoaded extends JuceAudioState {
  @override
  List<Object> get props => [];
}


class JuceAudioSettingsUpdatedState extends JuceAudioState {
  @override
  List<Object> get props => [];
}
