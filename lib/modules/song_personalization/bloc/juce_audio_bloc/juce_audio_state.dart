part of 'juce_audio_bloc.dart';

abstract class JuceAudioState extends BlocState {
  const JuceAudioState();
}

class JuceAudioInit extends JuceAudioState {
  @override
  List<Object> get props => [];
}

class JuceAudioLoaded extends JuceAudioState {
  @override
  List<Object> get props => [];
}

class JuceAudioSettingsUpdatedState extends JuceAudioState {
  @override
  List<Object> get props => [];
}

class JuceAudioExporting extends JuceAudioState {
  @override
  List<Object> get props => [];
}

class JuceAudioExported extends JuceAudioState {
  final String mixedAudioPath;

  const JuceAudioExported({required this.mixedAudioPath});

  @override
  List<Object> get props => [mixedAudioPath];
}

class JuceAudioError extends JuceAudioState {
  final Object error;

  const JuceAudioError({required this.error});

  @override
  List<Object> get props => [error];
}
