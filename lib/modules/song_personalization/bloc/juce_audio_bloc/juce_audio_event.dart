part of 'juce_audio_bloc.dart';

abstract class JuceAudioEvent extends BaseEvent {
  const JuceAudioEvent();
}

class JuceAudioLoadEvent extends JuceAudioEvent {
  @override
  List<Object> get props => [];
}

class JuceAudioEnableMetronomeEvent extends JuceAudioEvent {
  final String timeSignature;
  final int tempo;
  final String bgmPath;
  final double metronomeVol;

  const JuceAudioEnableMetronomeEvent({
    required this.bgmPath,
    required this.timeSignature,
    required this.tempo,
    this.metronomeVol = 1.0,
  });

  @override
  List<Object?> get props => [bgmPath, timeSignature, tempo];
}

class JuceAudioDisableMetronomeEvent extends JuceAudioEvent {
  @override
  List<Object?> get props => [];
}

class JuceAudioEnableGuideTrackEvent extends JuceAudioEvent {
  final String guidePath;
  final String bgmPath;
  final double guideVol;
  final double metronomeVol;


  const Ju<PERSON>AudioEnableGuideTrackEvent({
    required this.guidePath,
    required this.bgmPath,
    this.metronomeVol = 1.0,
    this.guideVol = 1.0,
  });

  @override
  List<Object?> get props => [guidePath, bgmPath];
}

class JuceAudioDisableGuideTrackEvent extends JuceAudioEvent {
  @override
  List<Object?> get props => [];
}
