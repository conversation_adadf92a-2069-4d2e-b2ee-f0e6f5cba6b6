import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:juce_mix_player/juce_mix_player.dart';
import 'package:melodyze/core/core_modules/file_downloader/file_downloader.dart';
import 'package:melodyze/core/generic_bloc/events.dart';
import 'package:melodyze/core/generic_bloc/safe_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/utilities/utils/utils.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/app_toast.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/core/wrappers/path_wrapper.dart';
import 'package:melodyze/juce_kit/juce_kit_wrapper.dart';

part 'juce_audio_event.dart';
part 'juce_audio_state.dart';

/// Enum representing different types of mixer tracks
enum MixerTrackType {
  bgm,
  guide,
  vocal;

  /// Converts enum value to string representation
  String createString() {
    return toString().split('.').last;
  }
}

/// JuceAudioBloc handles all audio mixing operations including BGM, guide tracks, and metronome.
///
/// This bloc is responsible for:
/// - File downloads for audio resources (BGM, guide tracks)
/// - Track management and mixing configuration
/// - Audio export and mixed file generation
/// - Metronome track creation
/// - Volume and effect controls
///
/// The mixed audio path can be shared with other components like AudioPlayerBloc for playback
/// or RecorderChannel for recording scenarios.
class JuceAudioBloc extends SafeBloc<JuceAudioEvent, JuceAudioState> {
  static const tag = "JuceAudioBloc";

  // Core mixing components
  final JuceMixer _juceMixer = JuceMixer();
  final Debouncer _debouncer = Debouncer();

  // Audio state
  double opDuration = 0.0;
  Map<MixerTrackType, MixerTrack> tracks = {};
  List<MixerTrack> metronomeTracks = [];

  // Settings state
  bool isGuideEnabled = false;
  bool isMetronomeEnabled = false;
  double guideVol = 1.0;
  double metronomeVol = 1.0;
  double bgmVol = 1.0;

  // File paths
  String? mixedAudioPath;
  String? downloadedBgmPath;
  String? downloadedGuidePath;
  String bgmPath = "";
  String guidePath = "";
  String timeSign = "";
  int tempo = 0;

  // Session state
  String? lastAnnotationId;
  String? lastSongId;

  JuceAudioBloc() : super(JuceAudioInit()) {
    on<JuceAudioLoadEvent>(_onLoad);
    on<JuceAudioEnableMetronomeEvent>(_onEnableMetronome);
    on<JuceAudioDisableMetronomeEvent>(_onDisableMetronome);
    on<JuceAudioEnableGuideTrackEvent>(_onEnableGuideTrack);
    on<JuceAudioDisableGuideTrackEvent>(_onDisableGuideTrack);
    on<JuceAudioExportEvent>(_onExportAudio);
    // on<JuceAudioClearTracksEvent>(_onClearTracks);
    add(JuceAudioLoadEvent());
  }

  Future<void> _onLoad(JuceAudioLoadEvent event, Emitter<JuceAudioState> emit) async {
    try {
      emit(JuceAudioLoaded());
    } catch (e) {
      logger.e("$tag: JuceAudioLoadEvent error: $e");
      emit(JuceAudioError(error: e));
    }
  }

  Future<void> _onEnableMetronome(JuceAudioEnableMetronomeEvent event, Emitter<JuceAudioState> emit) async {
    try {
      await _downloadBgm(event.bgmPath, 1.0); // Use default BGM volume

      tracks[MixerTrackType.bgm] = MixerTrack(
        id: MixerTrackType.bgm.createString(),
        path: downloadedBgmPath!,
        enabled: true,
        volume: bgmVol,
      );

      metronomeTracks = _juceMixer.createMetronomeTracks(
        tempo: event.tempo,
        timeSignature: event.timeSignature,
        volume: event.metronomeVol,
        hPath: await AssetPaths.extractAsset("assets/metronome_tone/met_h.wav"),
        lPath: await AssetPaths.extractAsset("assets/metronome_tone/met_l.wav"),
      );

      isMetronomeEnabled = true;
      metronomeVol = event.metronomeVol;
      timeSign = event.timeSignature;
      tempo = event.tempo;

      emit(JuceAudioSettingsUpdatedState());
      add(JuceAudioExportEvent());
      unawaited(DI().resolve<AppToast>().showToast('Metronome enabled'));
    } catch (e) {
      logger.e('$tag: Error enabling metronome: $e');
      emit(JuceAudioError(error: 'Error creating metronome: $e'));
    }
  }

  Future<void> _onDisableMetronome(JuceAudioDisableMetronomeEvent event, Emitter<JuceAudioState> emit) async {
    try {
      metronomeTracks = [];
      isMetronomeEnabled = false;

      emit(JuceAudioSettingsUpdatedState());
      add(JuceAudioExportEvent());
      unawaited(DI().resolve<AppToast>().showToast('Metronome disabled'));
    } catch (e) {
      logger.e('$tag: Error disabling metronome: $e');
      emit(JuceAudioError(error: 'Error disabling metronome: $e'));
    }
  }

  Future<void> _onEnableGuideTrack(JuceAudioEnableGuideTrackEvent event, Emitter<JuceAudioState> emit) async {
    try {
      await _downloadBgm(event.bgmPath, 1.0); // Use default BGM volume
      await _downloadGuideTrack(event.guidePath);

      tracks[MixerTrackType.guide] = MixerTrack(
        id: MixerTrackType.guide.createString(),
        path: downloadedGuidePath!,
        volume: event.guideVol,
        enabled: true,
      );

      isGuideEnabled = true;
      guideVol = event.guideVol;

      emit(JuceAudioSettingsUpdatedState());
      add(JuceAudioExportEvent());
      unawaited(DI().resolve<AppToast>().showToast('Guide enabled'));
    } catch (e) {
      logger.e('$tag: Error enabling guide track: $e');
      emit(JuceAudioError(error: 'Error creating guide track: $e'));
    }
  }

  Future<void> _onDisableGuideTrack(JuceAudioDisableGuideTrackEvent event, Emitter<JuceAudioState> emit) async {
    try {
      tracks.remove(MixerTrackType.guide);
      isGuideEnabled = false;

      emit(JuceAudioSettingsUpdatedState());
      add(JuceAudioExportEvent());
      unawaited(DI().resolve<AppToast>().showToast('Guide disabled'));
    } catch (e) {
      logger.e('$tag: Error disabling guide track: $e');
      emit(JuceAudioError(error: 'Error disabling guide track: $e'));
    }
  }

  Future<void> _onExportAudio(JuceAudioExportEvent event, Emitter<JuceAudioState> emit) async {
    try {
      emit(JuceAudioExporting());

      _logTrackInfo();
      final composeModel = await _createMixerComposeModel();
      await _juceMixer.export(composeModel);

      emit(JuceAudioExported(mixedAudioPath: mixedAudioPath!));
    } catch (e) {
      logger.e('$tag: Error exporting audio: $e');
      emit(JuceAudioError(error: 'Error creating mix: $e'));
    }
  }

  // Future<void> _onClearTracks(JuceAudioClearTracksEvent event, Emitter<JuceAudioState> emit) async {
  //   // Clear tracks if new annotation
  //   if (lastAnnotationId != event.annotationId) {
  //     metronomeTracks = [];
  //     downloadedBgmPath = null;
  //     downloadedGuidePath = null;
  //     tracks.clear();
  //     mixedAudioPath = null;

  //     isGuideEnabled = false;
  //     isMetronomeEnabled = false;

  //     logger.d('$tag: Cleared tracks for new annotation: ${event.annotationId}');
  //   }
  //   lastAnnotationId = event.annotationId;
  //   emit(JuceAudioSettingsUpdatedState());
  // }

  // File Download Methods
  /// Downloads BGM file if not already downloaded
  Future<void> _downloadBgm(String bgmUrl, double vol) async {
    if (downloadedBgmPath == null || downloadedBgmPath!.isEmpty) {
      try {
        final fileDownloader = FileDownloader();
        await fileDownloader.download(resource: bgmUrl, resourceType: FileResourceType.s3Url, type: FileType.bgm);
        downloadedBgmPath = fileDownloader.downloadedFile!.path;
        logger.d('$tag: BGM downloaded to: $downloadedBgmPath');
      } catch (e) {
        logger.e('$tag: Failed to download BGM', error: e);
        throw Exception('Failed to download BGM: $e');
      }
    }

    bgmVol = vol;
    tracks[MixerTrackType.bgm] = MixerTrack(
      id: MixerTrackType.bgm.createString(),
      path: downloadedBgmPath!,
      enabled: true,
      volume: vol,
    );
  }

  /// Downloads guide track file if not already downloaded
  Future<void> _downloadGuideTrack(String guideUrl) async {
    if (downloadedGuidePath == null || downloadedGuidePath!.isEmpty) {
      try {
        final fileDownloader = FileDownloader();
        await fileDownloader.download(resource: guideUrl, resourceType: FileResourceType.s3Url, type: FileType.guide);
        downloadedGuidePath = fileDownloader.downloadedFile!.path;
        logger.d('$tag: Guide track downloaded to: $downloadedGuidePath');
      } catch (e) {
        logger.e('$tag: Failed to download guide track', error: e);
        throw Exception('Failed to download guide track: $e');
      }
    }
  }

  // Audio Mixing Methods
  /// Logs information about current tracks for debugging
  void _logTrackInfo() {
    tracks.forEach((trackType, model) {
      logger.i('$tag: Track ID: ${model.id}, Path: ${model.path}: vol:${model.volume}: ${model.duration}: ${model.fromTime}: ${model.offset}');
    });
  }

  /// Creates a mixer compose model with current tracks and metronome
  Future<MixerComposeModel> _createMixerComposeModel() async {
    mixedAudioPath ??= '${await PathWrapper.getTempDownloadPath()}/mixed.wav';
    return MixerComposeModel(
      tracks: tracks.values.toList()..addAll(metronomeTracks),
      output: mixedAudioPath!,
    );
  }

  // Public Methods
  Future<double> getAudioFileDuration(String filePath) async {
    return await _juceMixer.getAudioFileDuration(filePath);
  }

  /// Gets the current mixed audio path
  String? get currentMixedAudioPath => mixedAudioPath;

  /// Checks if any tracks are currently enabled
  bool get hasActiveTracks => tracks.isNotEmpty || metronomeTracks.isNotEmpty;

  @override
  Future<void> close() async {
    logger.d("$tag: Disposing JuceAudioBloc resources");

    try {
      _debouncer.dispose();
      _juceMixer.dispose();

      logger.d("$tag: All resources disposed successfully");
    } catch (e) {
      logger.e("$tag: Error during resource disposal", error: e);
    }

    return super.close();
  }
}
