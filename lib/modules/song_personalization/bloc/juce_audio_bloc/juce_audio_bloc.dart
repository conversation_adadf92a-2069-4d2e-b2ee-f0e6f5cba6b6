import 'package:juce_mix_player/juce_mix_player.dart';
import 'package:melodyze/core/generic_bloc/events.dart';
import 'package:melodyze/core/generic_bloc/safe_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/juce_kit/juce_kit_wrapper.dart';

part 'juce_audio_event.dart';
part 'juce_audio_state.dart';

class JuceAudioBloc extends SafeBloc<JuceAudioEvent, JuceAudioState> {
  final JuceMixer _juceMixer = JuceMixer();

  double opDuration = 0.0;
  bool isGuideEnabled = false;
  bool isMetronomeEnabled = false;
  double guideVol = 1.0;
  double metronomeVol = 1.0;
  MixerComposeModel mixComposeModel = MixerComposeModel(tracks: [], outputDuration: 0);

  String bgmPath = "";
  String guidePath = "";
  String timeSign = "";
  int tempo = 0;

  JuceAudioBloc() : super(JuceAudioInit()) {
    on<JuceAudioLoadEvent>(_onLoad);
    on<JuceAudioEnableMetronomeEvent>(_onEnableMetronome);
    on<JuceAudioDisableMetronomeEvent>(_onDisableMetronome);
    on<JuceAudioEnableGuideTrackEvent>(_onEnableGuideTrack);
    on<JuceAudioDisableGuideTrackEvent>(_onDisableGuideTrack);
    add(JuceAudioLoadEvent());
  }

  Future<void> _onLoad(JuceAudioLoadEvent event, _) async {
    try {
      emit(JuceAudioLoaded());
    } catch (e) {
      logger.e("JuceAudioLoadEvent: $e");
    }
  }

  Future<void> _onEnableMetronome(JuceAudioEnableMetronomeEvent event, _) async {
    bgmPath = event.bgmPath;
    isMetronomeEnabled = true;
    metronomeVol = event.metronomeVol;
    timeSign = event.timeSignature;
    tempo = event.tempo;
    emit(JuceAudioSettingsUpdatedState());
  }

  Future<void> _onDisableMetronome(JuceAudioDisableMetronomeEvent event, _) async {
    isMetronomeEnabled = false;
    emit(JuceAudioSettingsUpdatedState());
  }

  Future<void> _onEnableGuideTrack(JuceAudioEnableGuideTrackEvent event, _) async {
    bgmPath = event.bgmPath;
    guidePath = event.guidePath;
    isGuideEnabled = true;
    guideVol = event.guideVol;
    emit(JuceAudioSettingsUpdatedState());
  }

  Future<void> _onDisableGuideTrack(JuceAudioDisableGuideTrackEvent event, _) async {
    isGuideEnabled = false;
    emit(JuceAudioSettingsUpdatedState());
  }

  Future<double> getAudioFileDuration(String filePath) async {
    return await _juceMixer.getAudioFileDuration(filePath);
  }

  Future<String> exportMixedAudio(String outputFile, double outputDuration) async {
    // TODO TO CHOOSE HOW TO CREATE MIX COMPOSE MODEL
    opDuration = outputDuration;
    await _juceMixer.export(mixComposeModel);
    return outputFile;
  }
}
