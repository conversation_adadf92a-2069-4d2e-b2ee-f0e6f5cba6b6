import 'dart:async';

import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:just_audio/just_audio.dart';
import 'package:melodyze/core/generic_bloc/events.dart';
import 'package:melodyze/core/generic_bloc/safe_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/utilities/utils/extensions.dart';
import 'package:melodyze/core/utilities/utils/utils.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';

// Define BLoC events
part 'audio_player_event.dart';
// Define BLoC states
part 'audio_player_state.dart';

/// AudioPlayerBloc handles core audio playback functionality.
///
/// This simplified bloc is responsible for:
/// - Core audio playback (play, pause, stop, seek)
/// - Volume control and progress tracking
/// - App lifecycle handling (pause on minimize, resume on show)
/// - Session state management (position restoration)
///
/// For audio mixing operations, use JuceAudioBloc which provides the mixed audio path
/// that can be loaded into this player for playback.
class AudioPlayerBloc extends SafeBloc<AudioPlayerEvent, AudioPlayerState> {
  static const tag = "AudioPlayerBloc";

  // Core audio player components
  final AudioPlayer _audioPlayer = AudioPlayer();
  final Debouncer _debouncer = Debouncer();

  // Lifecycle management
  AppLifecycleListener? _lifecycleListener;

  // Audio state
  Duration audioDuration = Duration.zero;

  // Session state
  AudioPlayerLoadEvent? lastLoadEvent;
  String? lastAnnotationId;
  String? lastSongId;
  bool? _lastPlayingStateBeforeMinimise;

  // Configuration
  final bool deleteTempDir;

  // Getters
  bool get isPlaying => _audioPlayer.playing;
  Duration get position => _audioPlayer.position;
  double get volume => _audioPlayer.volume;

  // Public methods
  /// Sets the playback position to the specified duration
  ///
  /// [value] The target position to seek to
  void setPosition(Duration value) {
    _audioPlayer.seek(value);
  }

  AudioPlayerBloc({this.deleteTempDir = true}) : super(AudioPlayerInitial()) {
    _setupEventHandlers();
    _setupAudioPlayerListeners();
    _setupJuceAudioListener();
  }

  /// Sets up all event handlers for core playback functionality
  void _setupEventHandlers() {
    // Core playback events
    on<AudioPlayerLoadEvent>(_onLoadAudio);
    on<AudioPlayerPlayEvent>(_onPlayAudio);
    on<AudioPlayerPauseEvent>(_onPauseAudio);
    on<AudioPlayerStopEvent>(_onStopAudio);

    // Audio control events
    on<AudioPlayerProgressUpdateEvent>(_onProgressUpdate);
    on<AudioPlayerSeekEvent>(_onSeekAudio);
    on<AudioPlayerChangeVolumeEvent>(_onChangeVolume);
  }

  void _setupAudioPlayerListeners() {
    _audioPlayer.playerStateStream.listen((state) async {
      if (state.processingState == ProcessingState.completed) {
        add(AudioPlayerStopEvent());
      }
    });

    _audioPlayer.positionStream.listen((position) async {
      if (isClosed) return;
      add(AudioPlayerProgressUpdateEvent(position: position));
    });
  }

  void _setupJuceAudioListener() {
    // _juceAudioListener = juceAudioBloc.stream.listen((state) {
    //   if (state is JuceAudioSettingsUpdatedState) {
    // TODO: Export the latest audio and load it in the audio player
    //     logger.d('$tag: JUCE audio settings updated');
    //   }
    // });

    logger.d('$tag: JUCE audio listener setup completed (placeholder for future integration)');
  }

  /// Handles session state management including position restoration
  ///
  /// [event] The load event containing session information
  /// [lastSeekPosition] The previous playback position to restore
  void _handleSessionState(AudioPlayerLoadEvent event, Duration lastSeekPosition) {
    // Seek to last position if same song
    if (lastSongId == event.songId) {
      _audioPlayer.seek(lastSeekPosition);
    }
    lastSongId = event.songId;

    // Update annotation tracking
    lastAnnotationId = event.annotationId;
  }

  void _setupAppLifecycleListener() {
    _lifecycleListener ??= AppLifecycleListener(
      onHide: () {
        _lastPlayingStateBeforeMinimise = isPlaying;
        add(AudioPlayerPauseEvent());
      },
      onShow: () {
        if (_lastPlayingStateBeforeMinimise == true) {
          add(AudioPlayerPlayEvent());
        }
      },
    );
  }

  Future<void> _onLoadAudio(AudioPlayerLoadEvent event, Emitter<AudioPlayerState> emit) async {
    try {
      _setupAppLifecycleListener();

      final playingState = _audioPlayer.playing;
      final lastSeekPosition = _audioPlayer.position;

      if (_audioPlayer.playing) {
        await _audioPlayer.stop();
      }

      if (event.path.isNullOrEmpty) {
        audioDuration = Duration.zero;
        emitError('Empty URL');
        return;
      }

      switch (event.type) {
        case AudioType.url:
          await _audioPlayer.setUrl(event.path);
          break;
        case AudioType.file:
          await _audioPlayer.setFilePath(event.path);
          break;
      }
      audioDuration = _audioPlayer.duration ?? Duration.zero;

      _handleSessionState(event, lastSeekPosition);

      emit(AudioPlayerLoaded());

      if ((lastLoadEvent == null && event.autoPlay) || playingState) {
        add(AudioPlayerPlayEvent());
      }
      lastLoadEvent = event;
    } catch (e) {
      logger.e(tag, error: e);
      emitError('Failed to load audio', error: e);
    }
  }

  Future<void> _onPlayAudio(AudioPlayerPlayEvent event, Emitter<AudioPlayerState> emit) async {
    final bgmStartTime = DateTime.now();
    logger.d('$tag: BGM started playing at: $bgmStartTime');
    emit(AudioPlayerPlaying(startTime: bgmStartTime));
    unawaited(_audioPlayer.play());
  }

  Future<void> _onPauseAudio(AudioPlayerPauseEvent event, Emitter<AudioPlayerState> emit) async {
    await _audioPlayer.pause();
    emit(AudioPlayerPaused());
  }

  Future<void> _onStopAudio(AudioPlayerStopEvent event, Emitter<AudioPlayerState> emit) async {
    await _audioPlayer.stop();
    await _audioPlayer.seek(Duration.zero);
    _lastPlayingStateBeforeMinimise = false;
    emit(AudioPlayerStopped());
  }

  void _onProgressUpdate(AudioPlayerProgressUpdateEvent event, Emitter<AudioPlayerState> emit) {
    emit(AudioPlayerProgressUpdated(position: event.position));
  }

  Future<void> _onSeekAudio(AudioPlayerSeekEvent event, Emitter<AudioPlayerState> emit) async {
    _debouncer.debounce(() async {
      Duration seekPosition = Duration(milliseconds: event.milliseconds.toInt());
      await _audioPlayer.seek(seekPosition);
    }, 30);
  }

  Future<void> _onChangeVolume(AudioPlayerChangeVolumeEvent event, Emitter<AudioPlayerState> emit) async {
    await _audioPlayer.setVolume(event.value);
  }

  @override
  Future<void> close() async {
    logger.d("$tag: Disposing AudioPlayerBloc resources");

    try {
      await _audioPlayer.dispose();
      _lifecycleListener?.dispose();
      _debouncer.dispose();

      logger.d("$tag: All resources disposed successfully");
    } catch (e) {
      logger.e("$tag: Error during resource disposal", error: e);
    }

    return super.close();
  }

  // Error Handling Methods
  /// Standardized error handling with logging and state emission
  void emitError(String message, {dynamic error}) {
    final errorMessage = error != null ? '$message: ${error.toString()}' : message;
    logger.e('$tag: $errorMessage', error: error);

    if (!isClosed) {
      emit(AudioPlayerError(error: errorMessage));
      add(AudioPlayerStopEvent());
    }
  }

  /// Handles and logs exceptions with context
  void handleException(String context, dynamic exception, {StackTrace? stackTrace}) {
    final errorMessage = 'Error in $context: ${exception.toString()}';
    logger.e('$tag: $errorMessage', error: exception, stackTrace: stackTrace);

    if (!isClosed) {
      emit(AudioPlayerError(error: errorMessage));
    }
  }
}
