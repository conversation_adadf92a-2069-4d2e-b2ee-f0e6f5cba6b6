import 'dart:async';

import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:juce_mix_player/juce_mix_player.dart';
import 'package:just_audio/just_audio.dart';
import 'package:melodyze/core/core_modules/file_downloader/file_downloader.dart';
import 'package:melodyze/core/generic_bloc/events.dart';
import 'package:melodyze/core/generic_bloc/safe_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/recorder/recorder_channel.dart';
// import 'package:melodyze/core/services/aws_client.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/utilities/utils/extensions.dart';
import 'package:melodyze/core/utilities/utils/utils.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/app_toast.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/core/wrappers/path_wrapper.dart';
import 'package:melodyze/juce_kit/juce_kit_wrapper.dart';
import 'package:melodyze/modules/song_personalization/bloc/juce_audio_bloc/juce_audio_bloc.dart';

// Define BLoC events
part 'audio_player_event.dart';
// Define BLoC states
part 'audio_player_state.dart';

enum MixerTrackType {
  bgm,
  guide,
  vocal;

  String createString() {
    return toString().split('.').last;
  }
}

class AudioPlayerBloc extends SafeBloc<AudioPlayerEvent, AudioPlayerState> {
  static const tag = "AudioPlayerBloc";
  final AudioPlayer _audioPlayer = AudioPlayer();
  final JuceMixer mixer = JuceMixer();
  final Debouncer _debouncer = Debouncer();

  AppLifecycleListener? _lifecycleListener;
  StreamSubscription<JuceAudioBloc>? _juceAudioListener;

  Duration audioDuration = Duration.zero;
  Map<MixerTrackType, MixerTrack> tracks = {};
  List<MixerTrack> metronomeTracks = [];

  String? mixedAudioPath;
  String? downloadedBgmPath;
  String? downloadedGuidePath;

  AudioPlayerLoadEvent? lastLoadEvent;
  String? lastAnnotationId;
  String? lastSongId;
  bool? _lastPlayingStateBeforeMinimise;
  bool operationInProgress = false;

  final bool deleteTempDir;
  RecorderChannel? record;

  bool get isPlaying => _audioPlayer.playing;
  Duration get position => _audioPlayer.position;
  double get volume => _audioPlayer.volume;

  void setPosition(Duration value) {
    _audioPlayer.seek(value);
  }

  AudioPlayerBloc({this.deleteTempDir = true}) : super(AudioPlayerInitial()) {
    _setupEventHandlers();
    _setupAudioPlayerListeners();
    _setupJuceAudioListener();
  }

  void _setupEventHandlers() {
    on<AudioPlayerLoadEvent>(_onLoadAudio);
    on<AudioPlayerPlayEvent>(_onPlayAudio);
    on<AudioPlayerPauseEvent>(_onPauseAudio);
    on<AudioPlayerStopEvent>(_onStopAudio);

    on<AudioPlayerProgressUpdateEvent>(_onProgressUpdate);
    on<AudioPlayerSeekEvent>(_onSeekAudio);
    on<AudioPlayerChangeVolumeEvent>(_onChangeVolume);

    on<EnableMetronomeEvent>(_onEnableMetronome);
    on<DisableMetronomeEvent>(_onDisableMetronome);
    on<EnableGuideTrackEvent>(_onEnableGuideTrack);
    on<DisableGuideTrackEvent>(_onDisableGuideTrack);
  }

  void _setupAudioPlayerListeners() {
    _audioPlayer.playerStateStream.listen((state) async {
      if (state.processingState == ProcessingState.completed) {
        add(AudioPlayerStopEvent());
      }
    });

    _audioPlayer.positionStream.listen((position) async {
      if (isClosed) return;
      add(AudioPlayerProgressUpdateEvent(position: position));
    });
  }

  void _setupJuceAudioListener() {
    // _juceAudioListener = juceAudioBloc.stream.listen((state) {
    //   if (state is JuceAudioSettingsUpdatedState) {
        // TODO: Export the latest audio and load it in the audio player
    //     logger.d('$tag: JUCE audio settings updated');
    //   }
    // });

    logger.d('$tag: JUCE audio listener setup completed (placeholder for future integration)');
  }

  void _handleSessionState(AudioPlayerLoadEvent event, Duration lastSeekPosition) {
    if (lastSongId == event.songId) {
      _audioPlayer.seek(lastSeekPosition);
    }
    lastSongId = event.songId;

    if (lastAnnotationId != event.annotationId) {
      metronomeTracks = [];
      downloadedBgmPath = null;
      downloadedGuidePath = null;
      tracks.clear();
    }
    lastAnnotationId = event.annotationId;
  }

  void _setupAppLifecycleListener() {
    _lifecycleListener ??= AppLifecycleListener(
      onHide: () {
        _lastPlayingStateBeforeMinimise = isPlaying;
        add(AudioPlayerPauseEvent());
      },
      onShow: () {
        if (_lastPlayingStateBeforeMinimise == true) {
          add(AudioPlayerPlayEvent());
        }
      },
    );
  }

  Future<void> _onLoadAudio(AudioPlayerLoadEvent event, Emitter<AudioPlayerState> emit) async {
    try {
      _setupAppLifecycleListener();

      final playingState = _audioPlayer.playing;
      final lastSeekPosition = _audioPlayer.position;

      if (_audioPlayer.playing) {
        await _audioPlayer.stop();
      }

      if (event.path.isNullOrEmpty) {
        audioDuration = Duration.zero;
        emitError('Empty URL');
        return;
      }

      switch (event.type) {
        case AudioType.url:
          await _audioPlayer.setUrl(event.path);
          break;
        case AudioType.file:
          await _audioPlayer.setFilePath(event.path);
          break;
      }
      audioDuration = _audioPlayer.duration ?? Duration.zero;

      _handleSessionState(event, lastSeekPosition);

      emit(AudioPlayerLoaded());

      if ((lastLoadEvent == null && event.autoPlay) || playingState) {
        add(AudioPlayerPlayEvent());
      }
      lastLoadEvent = event;
    } catch (e) {
      logger.e(tag, error: e);
      emitError('Failed to load audio', error: e);
    }
  }

  Future<void> _onPlayAudio(AudioPlayerPlayEvent event, Emitter<AudioPlayerState> emit) async {
    final bgmStartTime = DateTime.now();
    logger.d('$tag: BGM started playing at: $bgmStartTime');
    emit(AudioPlayerPlaying(startTime: bgmStartTime));
    unawaited(_audioPlayer.play());
  }

  Future<void> _onPauseAudio(AudioPlayerPauseEvent event, Emitter<AudioPlayerState> emit) async {
    await _audioPlayer.pause();
    emit(AudioPlayerPaused());
  }

  Future<void> _onStopAudio(AudioPlayerStopEvent event, Emitter<AudioPlayerState> emit) async {
    await _audioPlayer.stop();
    await _audioPlayer.seek(Duration.zero);
    _lastPlayingStateBeforeMinimise = false;
    emit(AudioPlayerStopped());
  }

  void _onProgressUpdate(AudioPlayerProgressUpdateEvent event, Emitter<AudioPlayerState> emit) {
    emit(AudioPlayerProgressUpdated(position: event.position));
  }

  Future<void> _onSeekAudio(AudioPlayerSeekEvent event, Emitter<AudioPlayerState> emit) async {
    _debouncer.debounce(() async {
      Duration seekPosition = Duration(milliseconds: event.milliseconds.toInt());
      await _audioPlayer.seek(seekPosition);
    }, 30);
  }

  Future<void> _onChangeVolume(AudioPlayerChangeVolumeEvent event, Emitter<AudioPlayerState> emit) async {
    await _audioPlayer.setVolume(event.value);
  }

  // Event Handlers - Audio Effects
  Future<void> _onEnableMetronome(EnableMetronomeEvent event, Emitter<AudioPlayerState> emit) async {
    try {
      await _downloadBgm(event.bgmS3Path, event.bgmVolume);
      tracks[MixerTrackType.bgm] = MixerTrack(
        id: MixerTrackType.bgm.createString(),
        path: downloadedBgmPath!,
        enabled: true,
        volume: event.bgmVolume,
      );

      metronomeTracks = mixer.createMetronomeTracks(
        tempo: event.tempo,
        timeSignature: event.timeSignature,
        volume: event.volume,
        hPath: await AssetPaths.extractAsset("assets/metronome_tone/met_h.wav"),
        lPath: await AssetPaths.extractAsset("assets/metronome_tone/met_l.wav"),
      );
      await exportAudio();
      unawaited(DI().resolve<AppToast>().showToast('Metronome enabled'));
    } catch (e) {
      emitError('Error creating metronome', error: e);
    }
  }

  Future<void> _onDisableMetronome(DisableMetronomeEvent event, Emitter<AudioPlayerState> emit) async {
    metronomeTracks = [];
    await exportAudio();
    unawaited(DI().resolve<AppToast>().showToast('Metronome disabled'));
  }

  Future<void> _onEnableGuideTrack(EnableGuideTrackEvent event, Emitter<AudioPlayerState> emit) async {
    try {
      await _downloadBgm(event.bgmS3Path, event.bgmVolume);
      await _downloadGuideTrack(event.guideS3Path);

      tracks[MixerTrackType.guide] = MixerTrack(
        id: MixerTrackType.guide.createString(),
        path: downloadedGuidePath!,
        volume: event.volume,
        enabled: true,
      );
      await exportAudio();
      unawaited(DI().resolve<AppToast>().showToast('Guide enabled'));
    } catch (e) {
      emitError('Error creating guide track', error: e);
    }
  }

  Future<void> _onDisableGuideTrack(DisableGuideTrackEvent event, Emitter<AudioPlayerState> emit) async {
    tracks.remove(MixerTrackType.guide);
    await exportAudio();
    unawaited(DI().resolve<AppToast>().showToast('Guide disabled'));
  }

  @override
  Future<void> close() async {
    logger.d("$tag: Disposing AudioPlayerBloc resources");

    try {
      await _audioPlayer.dispose();
      _lifecycleListener?.dispose();
      _debouncer.dispose();
      await _juceAudioListener?.cancel();
      mixer.dispose();

      logger.d("$tag: All resources disposed successfully");
    } catch (e) {
      logger.e("$tag: Error during resource disposal", error: e);
    }

    return super.close();
  }

  // File Download Methods
  /// Downloads BGM file if not already downloaded
  Future<void> _downloadBgm(String bgmUrl, double vol) async {
    if (downloadedBgmPath == null || downloadedBgmPath!.isEmpty) {
      try {
        final fileDownloader = FileDownloader();
        await fileDownloader.download(resource: bgmUrl, resourceType: FileResourceType.s3Url, type: FileType.bgm);
        downloadedBgmPath = fileDownloader.downloadedFile!.path;
        logger.d('$tag: BGM downloaded to: $downloadedBgmPath');
      } catch (e) {
        logger.e('$tag: Failed to download BGM', error: e);
        throw Exception('Failed to download BGM: $e');
      }
    }

    tracks[MixerTrackType.bgm] = MixerTrack(
      id: MixerTrackType.bgm.createString(),
      path: downloadedBgmPath!,
      enabled: true,
      volume: vol,
    );
  }

  /// Downloads guide track file if not already downloaded
  Future<void> _downloadGuideTrack(String guideUrl) async {
    if (downloadedGuidePath == null || downloadedGuidePath!.isEmpty) {
      try {
        final fileDownloader = FileDownloader();
        await fileDownloader.download(resource: guideUrl, resourceType: FileResourceType.s3Url, type: FileType.guide);
        downloadedGuidePath = fileDownloader.downloadedFile!.path;
        logger.d('$tag: Guide track downloaded to: $downloadedGuidePath');
      } catch (e) {
        logger.e('$tag: Failed to download guide track', error: e);
        throw Exception('Failed to download guide track: $e');
      }
    }
  }

  // Audio Mixing Methods
  /// Exports mixed audio with current tracks and metronome
  Future<void> exportAudio({bool? autoPlay}) async {
    try {
      _logTrackInfo();
      final composeModel = await _createMixerComposeModel();
      await mixer.export(composeModel);
      await _loadMixedAudio(autoPlay);
    } catch (e) {
      emitError('Error creating mix', error: e);
    }
  }

  /// Logs information about current tracks for debugging
  void _logTrackInfo() {
    tracks.forEach((trackType, model) {
      logger.i('$tag: Track ID: ${model.id}, Path: ${model.path}: vol:${model.volume}: ${model.duration}: ${model.fromTime}: ${model.offset}');
    });
  }

  /// Creates a mixer compose model with current tracks and metronome
  Future<MixerComposeModel> _createMixerComposeModel() async {
    mixedAudioPath ??= '${await PathWrapper.getTempDownloadPath()}/mixed.wav';
    return MixerComposeModel(
      tracks: tracks.values.toList()..addAll(metronomeTracks),
      output: mixedAudioPath!,
    );
  }

  /// Loads the mixed audio file into the player
  Future<void> _loadMixedAudio(bool? autoPlay) async {
    add(AudioPlayerLoadEvent(
      annotationId: lastAnnotationId!,
      songId: lastSongId!,
      path: mixedAudioPath!,
      type: AudioType.file,
      autoPlay: autoPlay ?? lastLoadEvent?.autoPlay ?? false,
      source: lastLoadEvent?.source ?? AudioPlaySource.def,
    ));
  }

  // Error Handling Methods
  /// Standardized error handling with logging and state emission
  void emitError(String message, {dynamic error}) {
    final errorMessage = error != null ? '$message: ${error.toString()}' : message;
    logger.e('$tag: $errorMessage', error: error);

    if (!isClosed) {
      emit(AudioPlayerError(error: errorMessage));
      add(AudioPlayerStopEvent());
    }
  }

  /// Handles and logs exceptions with context
  void handleException(String context, dynamic exception, {StackTrace? stackTrace}) {
    final errorMessage = 'Error in $context: ${exception.toString()}';
    logger.e('$tag: $errorMessage', error: exception, stackTrace: stackTrace);

    if (!isClosed) {
      emit(AudioPlayerError(error: errorMessage));
    }
  }
}


 // void setIfRecordingScreen(RecorderChannel? record) {
  //   this.record = record;
  // }

  /// Runs [heavyOperation] after [delay] and emits [AudioPlayerInitial] if
  /// [heavyOperation] doesn't complete within [delay].
  ///
  /// [heavyOperation] is an operation that is expected to take more than
  /// [delay] milliseconds to complete. If [heavyOperation] completes
  /// successfully, [operationInProgress] is set to false and the loading timer
  /// is cancelled. If [heavyOperation] throws an error, [operationInProgress]
  /// is set to false and the error is rethrown.
  ///
  /// [delay] defaults to 100 milliseconds.
  ///
  /// This function is intended to be used for operations that are expected to
  /// take more than 100 milliseconds to complete. It prevents the UI from
  /// freezing while the operation is running.
  ///
  /// Note that if [heavyOperation] completes before [delay] milliseconds have
  /// passed, the loading timer is cancelled and [operationInProgress] is set
  /// to false.
  // Future<void> withDelayedLoading(
  //   Future<void> Function() heavyOperation, {
  //   Duration delay = const Duration(milliseconds: 300),
  // }) async {
  //   if (operationInProgress) {
  //     return;
  //   }
  //   operationInProgress = true;
  //   logger.d("$tag: Starting withDelayedLoading with delay: $delay");
  //   Timer? loadingTimer = Timer(delay, () {
  //     logger.d("$tag: Emitting AudioPlayerInitial due to delay");
  //     emit(AudioPlayerInitial());
  //   });

  //   try {
  //     await heavyOperation();
  //     if (loadingTimer.isActive) {
  //       loadingTimer.cancel();
  //     }
  //     logger.d("$tag: Heavy operation completed successfully");
  //     operationInProgress = false;
  //     return;
  //   } catch (e) {
  //     if (loadingTimer.isActive) {
  //       loadingTimer.cancel();
  //     }
  //     logger.e("$tag: Error during heavy operation", error: e);
  //     operationInProgress = false;
  //     rethrow;
  //   }
  // }