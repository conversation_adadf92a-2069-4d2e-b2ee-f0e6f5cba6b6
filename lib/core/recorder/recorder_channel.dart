import 'dart:convert';

import 'package:flutter/services.dart';

class RecorderChannel {
  static const platform = MethodChannel('recorder_channel');
  static const eventChannel = EventChannel('recorder_channel_events');
  static const eventChannelPlayerProgress = EventChannel('recorder_channel_events_player_progress');
  static const eventChannelAudioLevels = EventChannel('recorder_channel_events_audio_levels');

  /// milliseconds
  static const progressUpdateInterval = 50; // 20 times per second
  static const audioLevelUpdateInterval = 100; // 10 times per second

  void Function(RecorderChannelState event)? recorderStateUpdate;
  void Function(int time)? playerProgressUpdate;
  void Function(double level)? audioLevelUpdate;

  RecorderChannel() {
      eventChannel.receiveBroadcastStream(eventChannel.name).forEach((event) {
        if (recorderStateUpdate != null) {
          RecorderChannelState state = RecorderChannelState.values.byName(event.toString());
          recorderStateUpdate!(state);
        }
      });
      eventChannelPlayerProgress.receiveBroadcastStream(eventChannelPlayerProgress.name).forEach((event) {
        if (playerProgressUpdate != null) {
          playerProgressUpdate!(event);
        }
      });
      eventChannelAudioLevels.receiveBroadcastStream(eventChannelAudioLevels.name).forEach((event) {
        if (audioLevelUpdate != null) {
          audioLevelUpdate!(event);
        }
      });
  }

  Future<void> prepare({required String recPath, required String bgmPath}) async {
    await platform.invokeMethod<void>('prepare', {
      'recPath': recPath,
      'bgmPath': bgmPath,
      'progressUpdateInterval': progressUpdateInterval,
      'audioLevelUpdateInterval': audioLevelUpdateInterval,
    });
  }

  /// Initiates the recording process and starts the player.
  ///
  /// This method performs the following steps:
  /// 1. Starts the recorder.
  /// 2. Starts the player.
  ///
  /// Returns the delay in milliseconds between starting the recorder and the player.
  /// If the delay cannot be determined, it returns 0.
  ///
  Future<int> start() async {
    int? timeDiff = await platform.invokeMethod<int>('start');
    return timeDiff ?? 0;
  }

  /// Initiates the recording process and starts the player.
  Future<void> stop() async {
    await platform.invokeMethod<void>('stop');
  }

  Future<bool> hasPermission() async {
    bool? value = await platform.invokeMethod<bool>('hasPermission');
    return value ?? false;
  }

  Future<bool> isRecording() async {
    bool? value = await platform.invokeMethod<bool>('isRecording');
    return value ?? false;
  }

  Future<void> selectDevice(String uid) async {
    await platform.invokeMethod<void>('selectDevice', {'uid': uid});
  }

  Future<List<RecorderDevice>> listInputDevices() async {
    try {
      String? jsonStr = await platform.invokeMethod<String>('listInputDevices');
      List<dynamic> jsonList = json.decode(jsonStr ?? "");
      List<RecorderDevice> devices = jsonList.map((json) => RecorderDevice.fromJson(json)).toList();
      return devices;
    } catch (err) {
      return [];
    }
  }

  Future<void> dispose() async {
    await platform.invokeMethod<void>('dispose');
  }

  /// Sets a listener for recorder state updates.
  void setRecorderStateListener(void Function(RecorderChannelState event)? recorderStateUpdate) {
    this.recorderStateUpdate = recorderStateUpdate;
  }

  /// Sets a listener for player progress updates in milliseconds.
  void setPlayerProgressListener(void Function(int time)? playerProgressUpdate) {
    this.playerProgressUpdate = playerProgressUpdate;
  }

  /// receives audio leves ranges from min -160 dBFS, max 0 dBFS for iOS
  void setAudioLevelUpdateListener(void Function(double level)? audioLevelUpdate) {
    this.audioLevelUpdate = audioLevelUpdate;
  }
}

class RecorderDevice {
  final String uid;
  final String portName;
  final String portType;

  RecorderDevice({
    required this.uid,
    required this.portName,
    required this.portType,
  });

  factory RecorderDevice.fromJson(Map<String, dynamic> json) {
    return RecorderDevice(
      uid: json['uid'] as String,
      portName: json['portName'] as String,
      portType: json['portType'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'portName': portName,
      'portType': portType,
    };
  }
}

enum RecorderChannelState { preparing, ready, recording, finishedSuccess, finishedFailed }
