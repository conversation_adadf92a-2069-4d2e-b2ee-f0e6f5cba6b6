import 'dart:io';

import 'package:path_provider/path_provider.dart' as path;

class PathWrapper {
  PathWrapper._();

  static String? _tempGetApplicationDocumentsDirectoryPath;

  static Future<String> getApplicationDocumentsDirectoryPath() async => _tempGetApplicationDocumentsDirectoryPath ??= (await path.getApplicationDocumentsDirectory()).path;

  static Future<String> getVocalRecordingPath() async {
    return '${await getApplicationDocumentsDirectoryPath()}/vocal.wav';
    // return "/sdcard/Documents/rec.wav";
  }

  static Future<String> getNormalizedVocalRecordingPath() async {
    return '${await getApplicationDocumentsDirectoryPath()}/normalized_vocal.wav';
  }

  static Future<String> getFinalRecordOutputPath() async {
    return '${await getApplicationDocumentsDirectoryPath()}/final_record_output.wav';
  }

  static Future<String> getTempDownloadPath() async {
    // '/' after `temp_download` is appended later
    final path = '${await getApplicationDocumentsDirectoryPath()}/temp_download';
    final directory = Directory(path);
    if (!directory.existsSync()) {
      await directory.create(recursive: true);
    }
    return path;
  }
}
